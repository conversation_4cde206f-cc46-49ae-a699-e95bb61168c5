{"rustc": 2830703817519440116, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"pkg-config\", \"unlock_notify\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"with-asan\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 12596189024591215039, "deps": [[3214373357989284387, "pkg_config", false, 13722549575836188398], [12933202132622624734, "vcpkg", false, 14228992002302374211], [13440421558185929429, "cc", false, 15792782792766554960]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsqlite3-sys-631008fa03f88a75\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}