{"rustc": 2830703817519440116, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 17939523789604233343, "deps": [[2609582021868829428, "serde_json", false, 2796256580716201733], [2942454115271623557, "serde", false, 12292384659409023714], [4824857623768494398, "cargo_toml", false, 17696818950787330368], [4899080583175475170, "semver", false, 8154650877351660920], [5165059047667588304, "tauri_winres", false, 15769821134185482569], [6913375703034175521, "schemars", false, 6140992560145472936], [7170110829644101142, "json_patch", false, 13425719783432115322], [9293239362693504808, "glob", false, 7163887854985599795], [11207653606310558077, "anyhow", false, 17377642646243837450], [12060164242600251039, "toml", false, 3447790895818973401], [13077543566650298139, "heck", false, 2480857360986416331], [15622660310229662834, "walkdir", false, 9928088884977183007], [16928111194414003569, "dirs", false, 6107436457851935825], [17233053221795943287, "tauri_utils", false, 13075863611494663809]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-222d3eda862d1ded\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}