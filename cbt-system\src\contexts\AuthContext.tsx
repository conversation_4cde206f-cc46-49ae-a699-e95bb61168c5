import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, LoginForm, ApiResponse } from '../types';
import { authService } from '../services/auth';
import { dbService } from '../services/database';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (credentials: LoginForm) => Promise<ApiResponse<User>>;
  logout: () => Promise<void>;
  register: (userData: any) => Promise<ApiResponse<User>>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<ApiResponse<void>>;
  isAuthenticated: boolean;
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  canAccessSchool: (schoolId: number) => boolean;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setLoading(true);
      
      // Initialize services
      await dbService.initialize();
      await authService.initialize();
      
      // Check if user is already authenticated
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Failed to initialize auth:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials: LoginForm): Promise<ApiResponse<User>> => {
    try {
      setLoading(true);
      const response = await authService.login(credentials);
      
      if (response.success && response.data) {
        setUser(response.data);
      }
      
      return response;
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Login failed',
        error: 'LOGIN_ERROR'
      };
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setLoading(true);
      await authService.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: any): Promise<ApiResponse<User>> => {
    try {
      setLoading(true);
      const response = await authService.register(userData);
      return response;
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        message: 'Registration failed',
        error: 'REGISTRATION_ERROR'
      };
    } finally {
      setLoading(false);
    }
  };

  const changePassword = async (
    currentPassword: string, 
    newPassword: string
  ): Promise<ApiResponse<void>> => {
    try {
      const response = await authService.changePassword(currentPassword, newPassword);
      return response;
    } catch (error) {
      console.error('Change password error:', error);
      return {
        success: false,
        message: 'Failed to change password',
        error: 'CHANGE_PASSWORD_ERROR'
      };
    }
  };

  const refreshUser = async (): Promise<void> => {
    try {
      if (user) {
        const updatedUser = await dbService.getUserById(user.id);
        setUser(updatedUser);
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
    }
  };

  const isAuthenticated = user !== null && user.isActive;

  const hasRole = (role: string): boolean => {
    return user?.role === role;
  };

  const hasAnyRole = (roles: string[]): boolean => {
    return user ? roles.includes(user.role) : false;
  };

  const canAccessSchool = (schoolId: number): boolean => {
    if (!user) return false;
    
    // Super admin can access all schools
    if (user.role === 'super_admin') return true;
    
    // Other users can only access their own school
    return user.schoolId === schoolId;
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    register,
    changePassword,
    isAuthenticated,
    hasRole,
    hasAnyRole,
    canAccessSchool,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component for protecting routes
interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string;
  requiredRoles?: string[];
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredRoles,
  fallback = <div className="flex items-center justify-center h-64">
    <div className="text-center">
      <h2 className="text-xl font-semibold text-gray-900">Access Denied</h2>
      <p className="text-gray-600">You don't have permission to access this page.</p>
    </div>
  </div>
}) => {
  const { loading, isAuthenticated, hasRole, hasAnyRole } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return fallback;
  }

  if (requiredRole && !hasRole(requiredRole)) {
    return fallback;
  }

  if (requiredRoles && !hasAnyRole(requiredRoles)) {
    return fallback;
  }

  return <>{children}</>;
};
