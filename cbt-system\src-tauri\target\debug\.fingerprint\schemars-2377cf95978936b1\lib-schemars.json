{"rustc": 2830703817519440116, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 7470064831895566790, "deps": [[2609582021868829428, "serde_json", false, 2796256580716201733], [2942454115271623557, "serde", false, 12292384659409023714], [5404511084185685755, "url", false, 5781958677773528237], [6913375703034175521, "build_script_build", false, 9122582115650006441], [6982418085031928086, "dyn_clone", false, 8732474981905653904], [14923790796823607459, "indexmap", false, 10519607266847988128], [15267671913832104935, "uuid1", false, 14406167567192512137], [16071897500792579091, "schemars_derive", false, 15927014537425627616]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-2377cf95978936b1\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}