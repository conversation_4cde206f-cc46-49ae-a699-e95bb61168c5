import Database from '@tauri-apps/plugin-sql';
import { User, School, Test } from '../types';

class DatabaseService {
  private db: Database | null = null;

  async initialize(): Promise<void> {
    try {
      this.db = await Database.load('sqlite:cbt_system.db');
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  async getDb(): Promise<Database> {
    if (!this.db) {
      await this.initialize();
    }
    return this.db!;
  }

  // User operations
  async createUser(userData: any): Promise<User | null> {
    const db = await this.getDb();
    const result = await db.execute(
      `INSERT INTO users (uuid, email, username, password_hash, first_name, last_name, role, school_id, phone, address, date_of_birth, is_active, is_verified)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userData.uuid,
        userData.email,
        userData.username,
        userData.passwordHash || userData.password_hash,
        userData.firstName,
        userData.lastName,
        userData.role,
        userData.schoolId || null,
        userData.phone || null,
        userData.address || null,
        userData.dateOfBirth || null,
        userData.isActive,
        userData.isVerified
      ]
    );

    if (result.lastInsertId) {
      return this.getUserById(result.lastInsertId);
    }
    return null;
  }

  async getUserById(id: number): Promise<User | null> {
    const db = await this.getDb();
    const result = await db.select<User[]>(
      'SELECT * FROM users WHERE id = ?',
      [id]
    );
    return result.length > 0 ? this.mapUserFromDb(result[0]) : null;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const db = await this.getDb();
    const result = await db.select<any[]>(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );
    return result.length > 0 ? this.mapUserFromDb(result[0]) : null;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | null> {
    const db = await this.getDb();
    const setClause = Object.keys(userData)
      .filter(key => key !== 'id')
      .map(key => `${this.camelToSnake(key)} = ?`)
      .join(', ');

    const values = Object.entries(userData)
      .filter(([key]) => key !== 'id')
      .map(([, value]) => value);

    await db.execute(
      `UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    );

    return this.getUserById(id);
  }

  async getUsers(filters: any = {}, pagination: any = {}): Promise<{ users: User[], total: number }> {
    const db = await this.getDb();
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (filters.role) {
      whereClause += ' AND role = ?';
      params.push(filters.role);
    }

    if (filters.schoolId) {
      whereClause += ' AND school_id = ?';
      params.push(filters.schoolId);
    }

    if (filters.search) {
      whereClause += ' AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Get total count
    const countResult = await db.select<{ count: number }[]>(
      `SELECT COUNT(*) as count FROM users ${whereClause}`,
      params
    );
    const total = countResult[0].count;

    // Get paginated results
    const limit = pagination.limit || 10;
    const offset = ((pagination.page || 1) - 1) * limit;
    
    const users = await db.select<any[]>(
      `SELECT * FROM users ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    return {
      users: users.map(user => this.mapUserFromDb(user)),
      total
    };
  }

  // School operations
  async createSchool(schoolData: any): Promise<School | null> {
    const db = await this.getDb();
    const result = await db.execute(
      `INSERT INTO schools (uuid, name, code, address, phone, email, principal_name, logo, is_active)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        schoolData.uuid,
        schoolData.name,
        schoolData.code,
        schoolData.address || null,
        schoolData.phone || null,
        schoolData.email || null,
        schoolData.principalName || null,
        schoolData.logo || null,
        schoolData.isActive
      ]
    );

    if (result.lastInsertId) {
      return this.getSchoolById(result.lastInsertId);
    }
    return null;
  }

  async getSchoolById(id: number): Promise<School | null> {
    const db = await this.getDb();
    const result = await db.select<any[]>(
      'SELECT * FROM schools WHERE id = ?',
      [id]
    );
    return result.length > 0 ? this.mapSchoolFromDb(result[0]) : null;
  }

  async getSchools(): Promise<School[]> {
    const db = await this.getDb();
    const schools = await db.select<any[]>(
      'SELECT * FROM schools WHERE is_active = TRUE ORDER BY name'
    );
    return schools.map(school => this.mapSchoolFromDb(school));
  }

  // Test operations
  async createTest(testData: any): Promise<Test | null> {
    const db = await this.getDb();
    const result = await db.execute(
      `INSERT INTO tests (uuid, title, description, subject_id, class_id, created_by, total_questions, total_marks, duration, passing_marks, instructions, start_time, end_time, is_randomized, show_results, allow_review, is_published, is_active)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        testData.uuid,
        testData.title,
        testData.description || null,
        testData.subjectId,
        testData.classId,
        testData.createdBy,
        testData.totalQuestions,
        testData.totalMarks,
        testData.duration,
        testData.passingMarks || null,
        testData.instructions || null,
        testData.startTime || null,
        testData.endTime || null,
        testData.isRandomized,
        testData.showResults,
        testData.allowReview,
        testData.isPublished,
        testData.isActive
      ]
    );

    if (result.lastInsertId) {
      return this.getTestById(result.lastInsertId);
    }
    return null;
  }

  async getTestById(id: number): Promise<Test | null> {
    const db = await this.getDb();
    const result = await db.select<any[]>(
      'SELECT * FROM tests WHERE id = ?',
      [id]
    );
    return result.length > 0 ? this.mapTestFromDb(result[0]) : null;
  }

  async getTests(filters: any = {}): Promise<Test[]> {
    const db = await this.getDb();
    let whereClause = 'WHERE is_active = TRUE';
    const params: any[] = [];

    if (filters.classId) {
      whereClause += ' AND class_id = ?';
      params.push(filters.classId);
    }

    if (filters.subjectId) {
      whereClause += ' AND subject_id = ?';
      params.push(filters.subjectId);
    }

    if (filters.createdBy) {
      whereClause += ' AND created_by = ?';
      params.push(filters.createdBy);
    }

    const tests = await db.select<any[]>(
      `SELECT * FROM tests ${whereClause} ORDER BY created_at DESC`,
      params
    );

    return tests.map(test => this.mapTestFromDb(test));
  }

  // Helper methods for mapping database results to TypeScript objects
  private mapUserFromDb(dbUser: any): User {
    return {
      id: dbUser.id,
      uuid: dbUser.uuid,
      email: dbUser.email,
      username: dbUser.username,
      passwordHash: dbUser.password_hash,
      firstName: dbUser.first_name,
      lastName: dbUser.last_name,
      role: dbUser.role,
      schoolId: dbUser.school_id,
      profilePicture: dbUser.profile_picture,
      phone: dbUser.phone,
      address: dbUser.address,
      dateOfBirth: dbUser.date_of_birth,
      isActive: dbUser.is_active,
      isVerified: dbUser.is_verified,
      lastLogin: dbUser.last_login,
      createdAt: dbUser.created_at,
      updatedAt: dbUser.updated_at
    };
  }

  private mapSchoolFromDb(dbSchool: any): School {
    return {
      id: dbSchool.id,
      uuid: dbSchool.uuid,
      name: dbSchool.name,
      code: dbSchool.code,
      address: dbSchool.address,
      phone: dbSchool.phone,
      email: dbSchool.email,
      principalName: dbSchool.principal_name,
      logo: dbSchool.logo,
      isActive: dbSchool.is_active,
      createdAt: dbSchool.created_at,
      updatedAt: dbSchool.updated_at
    };
  }

  private mapTestFromDb(dbTest: any): Test {
    return {
      id: dbTest.id,
      uuid: dbTest.uuid,
      title: dbTest.title,
      description: dbTest.description,
      subjectId: dbTest.subject_id,
      classId: dbTest.class_id,
      createdBy: dbTest.created_by,
      totalQuestions: dbTest.total_questions,
      totalMarks: dbTest.total_marks,
      duration: dbTest.duration,
      passingMarks: dbTest.passing_marks,
      instructions: dbTest.instructions,
      startTime: dbTest.start_time,
      endTime: dbTest.end_time,
      isRandomized: dbTest.is_randomized,
      showResults: dbTest.show_results,
      allowReview: dbTest.allow_review,
      isPublished: dbTest.is_published,
      isActive: dbTest.is_active,
      createdAt: dbTest.created_at,
      updatedAt: dbTest.updated_at
    };
  }

  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}

export const dbService = new DatabaseService();
