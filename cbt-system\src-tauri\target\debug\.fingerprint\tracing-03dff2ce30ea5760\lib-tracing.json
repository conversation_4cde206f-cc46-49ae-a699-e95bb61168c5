{"rustc": 2830703817519440116, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6355579909791343455, "path": 16800593539885100861, "deps": [[325572602735163265, "tracing_attributes", false, 4690829758987023149], [1906322745568073236, "pin_project_lite", false, 5735289483764551673], [3424551429995674438, "tracing_core", false, 4811476325857810453], [13066042571740262168, "log", false, 14279440916579484424]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-03dff2ce30ea5760\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}