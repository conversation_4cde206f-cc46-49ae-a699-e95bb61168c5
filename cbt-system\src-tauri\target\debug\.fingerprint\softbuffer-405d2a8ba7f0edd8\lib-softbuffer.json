{"rustc": 2830703817519440116, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 4370658965284248499, "deps": [[376837177317575824, "build_script_build", false, 14250854035540959112], [4143744114649553716, "raw_window_handle", false, 12372018778488709184], [10281541584571964250, "windows_sys", false, 1649909003814141700], [13066042571740262168, "log", false, 14279440916579484424]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-405d2a8ba7f0edd8\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}