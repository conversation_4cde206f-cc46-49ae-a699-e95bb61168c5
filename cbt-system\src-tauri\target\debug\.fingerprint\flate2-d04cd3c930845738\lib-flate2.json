{"rustc": 2830703817519440116, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2225463790103693989, "path": 1116374142960516676, "deps": [[7312356825837975969, "crc32fast", false, 13514893943270591553], [7636735136738807108, "miniz_oxide", false, 17917624453200485427]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-d04cd3c930845738\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}