{"name": "cbt-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@headlessui/react": "^2.2.8", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@tailwindcss/forms": "^0.5.10", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2.4.0", "@tauri-apps/plugin-fs": "^2.4.2", "@tauri-apps/plugin-notification": "^2.3.1", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-sql": "^2.3.0", "@tauri-apps/plugin-store": "^2.4.0", "@types/react-router-dom": "^5.3.3", "date-fns": "^4.1.0", "lucide-react": "^0.544.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "react-router-dom": "^7.9.1", "recharts": "^3.2.0", "zod": "^4.1.8"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.13", "@tauri-apps/cli": "^2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.13", "typescript": "~5.8.3", "vite": "^7.0.4"}}