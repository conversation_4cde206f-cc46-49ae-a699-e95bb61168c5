{"rustc": 2830703817519440116, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 13671742598898157133, "deps": [[5103565458935487, "futures_io", false, 9469164592304382141], [1615478164327904835, "pin_utils", false, 11308048378148095044], [1906322745568073236, "pin_project_lite", false, 5735289483764551673], [7013762810557009322, "futures_sink", false, 2244984468082429009], [7620660491849607393, "futures_core", false, 9385414846926858188], [14767213526276824509, "slab", false, 16798774988582326862], [15932120279885307830, "memchr", false, 10122364049294976872], [16240732885093539806, "futures_task", false, 8342288194159562261]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-5a58979c540d8d95\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}