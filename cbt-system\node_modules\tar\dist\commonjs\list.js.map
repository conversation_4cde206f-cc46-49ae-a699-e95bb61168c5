{"version": 3, "file": "list.js", "sourceRoot": "", "sources": ["../../src/list.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS;AACT,yDAA0C;AAC1C,sDAAwB;AACxB,+BAAqC;AACrC,uDAA+C;AAM/C,yCAAmC;AACnC,2EAAkE;AAElE,MAAM,mBAAmB,GAAG,CAAC,GAAe,EAAE,EAAE;IAC9C,MAAM,WAAW,GAAG,GAAG,CAAC,WAAW,CAAA;IACnC,GAAG,CAAC,WAAW;QACb,WAAW,CAAC,CAAC;YACX,CAAC,CAAC,EAAE;gBACF,WAAW,CAAC,CAAC,CAAC,CAAA;gBACd,CAAC,CAAC,MAAM,EAAE,CAAA;YACZ,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAA;AACrB,CAAC,CAAA;AAED,yDAAyD;AACzD,6CAA6C;AACtC,MAAM,WAAW,GAAG,CAAC,GAAe,EAAE,KAAe,EAAE,EAAE;IAC9D,MAAM,GAAG,GAAG,IAAI,GAAG,CACjB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAA,gDAAoB,EAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAChD,CAAA;IACD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;IAEzB,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,EAAW,EAAE;QACvD,MAAM,IAAI,GAAG,CAAC,IAAI,IAAA,YAAK,EAAC,IAAI,CAAC,CAAC,IAAI,IAAI,GAAG,CAAA;QACzC,IAAI,GAAY,CAAA;QAChB,IAAI,IAAI,KAAK,IAAI;YAAE,GAAG,GAAG,KAAK,CAAA;aACzB,CAAC;YACJ,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YACvB,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;gBACpB,GAAG,GAAG,CAAC,CAAA;YACT,CAAC;iBAAM,CAAC;gBACN,GAAG,GAAG,MAAM,CAAC,IAAA,cAAO,EAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YACnC,CAAC;QACH,CAAC;QAED,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAClB,OAAO,GAAG,CAAA;IACZ,CAAC,CAAA;IAED,GAAG,CAAC,MAAM;QACR,MAAM,CAAC,CAAC;YACN,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CACd,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,IAAA,gDAAoB,EAAC,IAAI,CAAC,CAAC;YAC7D,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAA,gDAAoB,EAAC,IAAI,CAAC,CAAC,CAAA;AAChD,CAAC,CAAA;AA5BY,QAAA,WAAW,eA4BvB;AAED,MAAM,YAAY,GAAG,CAAC,GAAuB,EAAE,EAAE;IAC/C,MAAM,CAAC,GAAG,IAAI,iBAAM,CAAC,GAAG,CAAC,CAAA;IACzB,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;IACrB,IAAI,EAAE,CAAA;IACN,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,iBAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC9B,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAA;QACpD,IAAI,IAAI,CAAC,IAAI,GAAG,QAAQ,EAAE,CAAC;YACzB,CAAC,CAAC,GAAG,CAAC,iBAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAA;QAC9B,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,GAAG,CAAC,CAAA;YACX,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;YACxC,EAAE,GAAG,iBAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;YAC3B,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;gBACvB,MAAM,SAAS,GAAG,iBAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA;gBACxD,GAAG,IAAI,SAAS,CAAA;gBAChB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAA;YACrC,CAAC;YACD,CAAC,CAAC,GAAG,EAAE,CAAA;QACT,CAAC;IACH,CAAC;YAAS,CAAC;QACT,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,iBAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;gBAChB,oBAAoB;YACtB,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC,CAAA,CAAC;QACjB,CAAC;IACH,CAAC;AACH,CAAC,CAAA;AAED,MAAM,QAAQ,GAAG,CACf,GAAmB,EACnB,MAAgB,EACD,EAAE;IACjB,MAAM,KAAK,GAAG,IAAI,iBAAM,CAAC,GAAG,CAAC,CAAA;IAC7B,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAA;IAEpD,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;IACrB,MAAM,CAAC,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC9C,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QACzB,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;QAExB,iBAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACzB,IAAI,EAAE,EAAE,CAAC;gBACP,MAAM,CAAC,EAAE,CAAC,CAAA;YACZ,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;oBACtC,QAAQ,EAAE,QAAQ;oBAClB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC,CAAA;gBACF,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;gBAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpB,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IACF,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AAEY,QAAA,IAAI,GAAG,IAAA,6BAAW,EAC7B,YAAY,EACZ,QAAQ,EACR,GAAG,CAAC,EAAE,CAAC,IAAI,iBAAM,CAAC,GAAG,CAA4B,EACjD,GAAG,CAAC,EAAE,CAAC,IAAI,iBAAM,CAAC,GAAG,CAAC,EACtB,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;IACb,IAAI,KAAK,EAAE,MAAM;QAAE,IAAA,mBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IAC1C,IAAI,CAAC,GAAG,CAAC,QAAQ;QAAE,mBAAmB,CAAC,GAAG,CAAC,CAAA;AAC7C,CAAC,CACF,CAAA", "sourcesContent": ["// tar -t\nimport * as fsm from '@isaacs/fs-minipass'\nimport fs from 'node:fs'\nimport { dirname, parse } from 'path'\nimport { makeCommand } from './make-command.js'\nimport {\n  TarOptions,\n  TarOptionsFile,\n  TarOptionsSyncFile,\n} from './options.js'\nimport { Parser } from './parse.js'\nimport { stripTrailingSlashes } from './strip-trailing-slashes.js'\n\nconst onReadEntryFunction = (opt: TarOptions) => {\n  const onReadEntry = opt.onReadEntry\n  opt.onReadEntry =\n    onReadEntry ?\n      e => {\n        onReadEntry(e)\n        e.resume()\n      }\n    : e => e.resume()\n}\n\n// construct a filter that limits the file entries listed\n// include child entries if a dir is included\nexport const filesFilter = (opt: TarOptions, files: string[]) => {\n  const map = new Map<string, boolean>(\n    files.map(f => [stripTrailingSlashes(f), true]),\n  )\n  const filter = opt.filter\n\n  const mapHas = (file: string, r: string = ''): boolean => {\n    const root = r || parse(file).root || '.'\n    let ret: boolean\n    if (file === root) ret = false\n    else {\n      const m = map.get(file)\n      if (m !== undefined) {\n        ret = m\n      } else {\n        ret = mapHas(dirname(file), root)\n      }\n    }\n\n    map.set(file, ret)\n    return ret\n  }\n\n  opt.filter =\n    filter ?\n      (file, entry) =>\n        filter(file, entry) && mapHas(stripTrailingSlashes(file))\n    : file => mapHas(stripTrailingSlashes(file))\n}\n\nconst listFileSync = (opt: TarOptionsSyncFile) => {\n  const p = new Parser(opt)\n  const file = opt.file\n  let fd\n  try {\n    const stat = fs.statSync(file)\n    const readSize = opt.maxReadSize || 16 * 1024 * 1024\n    if (stat.size < readSize) {\n      p.end(fs.readFileSync(file))\n    } else {\n      let pos = 0\n      const buf = Buffer.allocUnsafe(readSize)\n      fd = fs.openSync(file, 'r')\n      while (pos < stat.size) {\n        const bytesRead = fs.readSync(fd, buf, 0, readSize, pos)\n        pos += bytesRead\n        p.write(buf.subarray(0, bytesRead))\n      }\n      p.end()\n    }\n  } finally {\n    if (typeof fd === 'number') {\n      try {\n        fs.closeSync(fd)\n        /* c8 ignore next */\n      } catch (er) {}\n    }\n  }\n}\n\nconst listFile = (\n  opt: TarOptionsFile,\n  _files: string[],\n): Promise<void> => {\n  const parse = new Parser(opt)\n  const readSize = opt.maxReadSize || 16 * 1024 * 1024\n\n  const file = opt.file\n  const p = new Promise<void>((resolve, reject) => {\n    parse.on('error', reject)\n    parse.on('end', resolve)\n\n    fs.stat(file, (er, stat) => {\n      if (er) {\n        reject(er)\n      } else {\n        const stream = new fsm.ReadStream(file, {\n          readSize: readSize,\n          size: stat.size,\n        })\n        stream.on('error', reject)\n        stream.pipe(parse)\n      }\n    })\n  })\n  return p\n}\n\nexport const list = makeCommand(\n  listFileSync,\n  listFile,\n  opt => new Parser(opt) as Parser & { sync: true },\n  opt => new Parser(opt),\n  (opt, files) => {\n    if (files?.length) filesFilter(opt, files)\n    if (!opt.noResume) onReadEntryFunction(opt)\n  },\n)\n"]}