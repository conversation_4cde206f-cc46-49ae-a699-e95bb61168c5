{"rustc": 2830703817519440116, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 2225463790103693989, "path": 13543442863517492358, "deps": [[6941104557053927479, "embed_resource", false, 3411094705181627080], [12060164242600251039, "toml", false, 3447790895818973401]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-c3bf2b5c6e012323\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}