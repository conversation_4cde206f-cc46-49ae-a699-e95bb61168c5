import { invoke } from '@tauri-apps/api/core';
import { load } from '@tauri-apps/plugin-store';
import { User, LoginForm, ApiResponse } from '../types';
import { dbService } from './database';

class AuthService {
  private store: any = null;
  private currentUser: User | null = null;

  async initialize(): Promise<void> {
    try {
      this.store = await load('auth.dat', { defaults: {}, autoSave: true });
      await this.loadCurrentUser();
    } catch (error) {
      console.error('Failed to initialize auth service:', error);
    }
  }

  async login(credentials: LoginForm): Promise<ApiResponse<User>> {
    try {
      // Get user from database
      const user = await dbService.getUserByEmail(credentials.email);
      
      if (!user) {
        return {
          success: false,
          message: 'Invalid email or password',
          error: 'USER_NOT_FOUND'
        };
      }

      if (!user.isActive) {
        return {
          success: false,
          message: 'Account is deactivated',
          error: 'ACCOUNT_DEACTIVATED'
        };
      }

      // Verify password using <PERSON><PERSON> command
      const isValidPassword = await invoke<boolean>('verify_password', {
        password: credentials.password,
        hash: user.passwordHash
      });

      if (!isValidPassword) {
        return {
          success: false,
          message: 'Invalid email or password',
          error: 'INVALID_PASSWORD'
        };
      }

      // Update last login
      await dbService.updateUser(user.id, {
        lastLogin: new Date().toISOString()
      });

      // Store user session
      await this.setCurrentUser(user);

      return {
        success: true,
        message: 'Login successful',
        data: user
      };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Login failed',
        error: 'LOGIN_ERROR'
      };
    }
  }

  async logout(): Promise<void> {
    try {
      this.currentUser = null;
      if (this.store) {
        await this.store.delete('currentUser');
        await this.store.save();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  async getCurrentUser(): Promise<User | null> {
    if (this.currentUser) {
      return this.currentUser;
    }
    
    await this.loadCurrentUser();
    return this.currentUser;
  }

  async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user !== null && user.isActive;
  }

  async hasRole(role: string): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user?.role === role;
  }

  async hasAnyRole(roles: string[]): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user ? roles.includes(user.role) : false;
  }

  async canAccessSchool(schoolId: number): Promise<boolean> {
    const user = await this.getCurrentUser();
    if (!user) return false;

    // Super admin can access all schools
    if (user.role === 'super_admin') return true;

    // Other users can only access their own school
    return user.schoolId === schoolId;
  }

  async register(userData: any): Promise<ApiResponse<User>> {
    try {
      // Check if user already exists
      const existingUser = await dbService.getUserByEmail(userData.email);
      if (existingUser) {
        return {
          success: false,
          message: 'User with this email already exists',
          error: 'USER_EXISTS'
        };
      }

      // Hash password using Tauri command
      const passwordHash = await invoke<string>('hash_password', {
        password: userData.password
      });

      // Create user
      const newUser = await dbService.createUser({
        ...userData,
        passwordHash,
        uuid: await this.generateUuid(),
        isActive: true,
        isVerified: false
      });

      return {
        success: true,
        message: 'User registered successfully',
        data: newUser || undefined
      };
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        message: 'Registration failed',
        error: 'REGISTRATION_ERROR'
      };
    }
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<void>> {
    try {
      const user = await this.getCurrentUser();
      if (!user) {
        return {
          success: false,
          message: 'User not authenticated',
          error: 'NOT_AUTHENTICATED'
        };
      }

      // Verify current password
      const isValidPassword = await invoke<boolean>('verify_password', {
        password: currentPassword,
        hash: user.passwordHash
      });

      if (!isValidPassword) {
        return {
          success: false,
          message: 'Current password is incorrect',
          error: 'INVALID_PASSWORD'
        };
      }

      // Hash new password
      const newPasswordHash = await invoke<string>('hash_password', {
        password: newPassword
      });

      // Update password in database
      await dbService.updateUser(user.id, {
        passwordHash: newPasswordHash
      });

      return {
        success: true,
        message: 'Password changed successfully'
      };
    } catch (error) {
      console.error('Change password error:', error);
      return {
        success: false,
        message: 'Failed to change password',
        error: 'CHANGE_PASSWORD_ERROR'
      };
    }
  }

  private async setCurrentUser(user: User): Promise<void> {
    try {
      this.currentUser = user;
      if (this.store) {
        await this.store.set('currentUser', user);
        await this.store.save();
      }
    } catch (error) {
      console.error('Failed to store current user:', error);
    }
  }

  private async loadCurrentUser(): Promise<void> {
    try {
      if (this.store) {
        const storedUser = await this.store.get('currentUser');
        if (storedUser) {
          // Verify user still exists and is active
          const user = await dbService.getUserById(storedUser.id);
          if (user && user.isActive) {
            this.currentUser = user;
          } else {
            // Clear invalid session
            await this.logout();
          }
        }
      }
    } catch (error) {
      console.error('Failed to load current user:', error);
    }
  }

  private async generateUuid(): Promise<string> {
    return await invoke<string>('generate_uuid');
  }
}

export const authService = new AuthService();
