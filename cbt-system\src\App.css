@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Inter', system-ui, sans-serif;
  background-color: #f9fafb;
  color: #111827;
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-secondary-200 p-6;
  }

  .input-field {
    @apply block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }

  .sidebar-link {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }

  .sidebar-link-active {
    @apply bg-primary-100 text-primary-900 border-r-2 border-primary-600;
  }

  .sidebar-link-inactive {
    @apply text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900;
  }
}
