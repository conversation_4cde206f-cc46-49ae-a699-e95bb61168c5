{"rustc": 2830703817519440116, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 11726333947473564109, "deps": [[373107762698212489, "proc_macro2", false, 14474029735085470692], [1678291836268844980, "brotli", false, 4195843696484792876], [2609582021868829428, "serde_json", false, 2796256580716201733], [2942454115271623557, "serde", false, 12292384659409023714], [4537297827336760846, "thiserror", false, 3292844830771133402], [4899080583175475170, "semver", false, 8154650877351660920], [5404511084185685755, "url", false, 5781958677773528237], [7170110829644101142, "json_patch", false, 13425719783432115322], [7392050791754369441, "ico", false, 4197005473935762541], [9857275760291862238, "sha2", false, 17610110145684034010], [12687914511023397207, "png", false, 12099877647681194978], [13077212702700853852, "base64", false, 13237125897986353216], [15267671913832104935, "uuid", false, 14406167567192512137], [15622660310229662834, "walkdir", false, 9928088884977183007], [17233053221795943287, "tauri_utils", false, 13075863611494663809], [17332570067994900305, "syn", false, 139270271993966230], [17990358020177143287, "quote", false, 11880094780238907154]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-b8b646959919c597\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}